import { useState, useEffect } from 'react';
import { getSignedUrl } from '../utils/fileUtils';
import { supabase } from '../lib/supabase';

/**
 * Debug page to investigate the PDF file access issue
 */
export const DebugFileAccess = () => {
  const [results, setResults] = useState<string[]>([]);
  const [storageFiles, setStorageFiles] = useState<any[]>([]);

  const addResult = (message: string) => {
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  // Test URLs from the database
  const testCases = [
    {
      name: 'PDF File',
      url: 'https://peuelelfodoiyvmjscfg.supabase.co/storage/v1/object/public/verbrauchsrechnungen/03ef9cf2-8a6f-4015-9862-06dc041c64a6/verbrauchsrechnung1_1749240687628_Rechnung-Muster-2.pdf',
      bucket: 'verbrauchsrechnungen',
      expectedPath: '03ef9cf2-8a6f-4015-9862-06dc041c64a6/verbrauchsrechnung1_1749240687628_Rechnung-Muster-2.pdf'
    },
    {
      name: 'Building Image',
      url: 'https://peuelelfodoiyvmjscfg.supabase.co/storage/v1/object/public/gebaeudebilder/76961f81-1650-41d7-9116-0fb510683574/1749227833471_hausbild.jpeg',
      bucket: 'gebaeudebilder',
      expectedPath: '76961f81-1650-41d7-9116-0fb510683574/1749227833471_hausbild.jpeg'
    }
  ];

  useEffect(() => {
    const debugFileAccess = async () => {
      addResult('🔍 Starting debug investigation...');

      // Test each case
      for (const testCase of testCases) {
        addResult(`\n🧪 Testing ${testCase.name}...`);
        addResult(`📋 URL: ${testCase.url}`);
        addResult(`📋 Bucket: ${testCase.bucket}`);
        addResult(`📋 Expected Path: ${testCase.expectedPath}`);

        // First, let's list files in the bucket to see what's actually there
        try {
          addResult(`📁 Listing files in ${testCase.bucket} bucket...`);

          // Try to list files in the root of the bucket
          const { data: rootFiles, error: rootError } = await supabase.storage
            .from(testCase.bucket)
            .list('', { limit: 100 });

          if (rootError) {
            addResult(`❌ Error listing root files: ${rootError.message}`);
          } else {
            addResult(`📁 Found ${rootFiles?.length || 0} items in root`);
            if (testCase.bucket === 'verbrauchsrechnungen') {
              setStorageFiles(rootFiles || []);
            }

            // If we found directories, let's explore them
            for (const item of rootFiles || []) {
              if (item.name && !item.name.includes('.')) {
                // This might be a directory
                addResult(`📂 Exploring directory: ${item.name}`);
                const { data: dirFiles, error: dirError } = await supabase.storage
                  .from(testCase.bucket)
                  .list(item.name, { limit: 100 });

                if (dirError) {
                  addResult(`❌ Error listing directory ${item.name}: ${dirError.message}`);
                } else {
                  addResult(`📁 Found ${dirFiles?.length || 0} files in ${item.name}`);
                  dirFiles?.forEach(file => {
                    addResult(`  📄 ${item.name}/${file.name}`);
                  });
                }
              }
            }
          }
        } catch (error) {
          addResult(`❌ Exception listing files: ${error.message}`);
        }

        // Test direct signed URL creation with expected path
        addResult(`🔍 Testing direct signed URL creation with expected path for ${testCase.name}...`);
        try {
          const { data: directSignedData, error: directSignedError } = await supabase.storage
            .from(testCase.bucket)
            .createSignedUrl(testCase.expectedPath, 3600);

          if (directSignedError) {
            addResult(`❌ Direct signed URL creation failed for ${testCase.name}: ${directSignedError.message}`);
          } else {
            addResult(`✅ Direct signed URL creation succeeded for ${testCase.name}`);
          }
        } catch (error) {
          addResult(`❌ Exception in direct signed URL creation for ${testCase.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }

        // Now test the URL parsing and signed URL creation
        addResult(`🔍 Testing URL parsing and signed URL creation for ${testCase.name}...`);
        try {
          const signedUrl = await getSignedUrl(testCase.url, testCase.bucket);
          if (signedUrl) {
            addResult(`✅ Successfully created signed URL for ${testCase.name}`);
          } else {
            addResult(`❌ Failed to create signed URL for ${testCase.name}`);
          }
        } catch (error) {
          addResult(`❌ Exception creating signed URL for ${testCase.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    };

    debugFileAccess();
  }, []);

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-gray-800 mb-6">
        Debug File Access Issue
      </h1>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Test Cases</h2>
        <div className="space-y-4">
          {testCases.map((testCase, index) => (
            <div key={index} className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-2">{testCase.name}</h3>
              <p><strong>URL:</strong></p>
              <code className="text-sm break-all block mb-2">{testCase.url}</code>
              <p><strong>Bucket:</strong> {testCase.bucket}</p>
              <p><strong>Expected Path:</strong> <code className="text-sm">{testCase.expectedPath}</code></p>
            </div>
          ))}
        </div>
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Storage Files Found</h2>
        <div className="bg-gray-50 p-4 rounded-lg max-h-60 overflow-y-auto">
          {storageFiles.length === 0 ? (
            <p className="text-gray-500">No files listed yet...</p>
          ) : (
            <ul className="space-y-1">
              {storageFiles.map((file, index) => (
                <li key={index} className="text-sm font-mono">
                  📄 {file.name} ({file.id})
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Debug Log</h2>
        <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto">
          {results.length === 0 ? (
            <p>No debug output yet...</p>
          ) : (
            results.map((result, index) => (
              <div key={index} className="mb-1">
                {result}
              </div>
            ))
          )}
        </div>
      </div>

      <div className="bg-blue-50 p-4 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">Instructions:</h3>
        <ol className="list-decimal list-inside text-blue-700 space-y-1">
          <li>Open the browser developer tools (F12)</li>
          <li>Go to the Console tab</li>
          <li>Look for detailed debug messages starting with 🔍</li>
          <li>Check the debug log above for file listing results</li>
          <li>Compare the actual file paths with what the URL parsing extracts</li>
        </ol>
      </div>
    </div>
  );
};
