import { supabase } from '../lib/supabase';

/**
 * Gets a signed URL for a file in Supabase storage
 * Handles both direct file paths and full URLs (including public URLs)
 * 
 * @param path The file path or full URL
 * @param bucket The storage bucket name
 * @returns A signed URL that can be used to access the file
 */
export const getSignedUrl = async (path: string, bucket: string): Promise<string | null> => {
  try {
    let filePath: string;

    // Check if path is a full URL or just a file path
    if (path.startsWith('http')) {
      // Extract the file path from the full URL
      const urlParts = path.split('/');
      
      // Find the bucket in the URL
      const bucketIndex = urlParts.findIndex(part => part === bucket);

      // If we can't find the bucket directly, it might be because the URL contains 'public' or other segments
      if (bucketIndex === -1) {
        // Try to find the bucket after 'object/public/' or 'object/sign/'
        const publicIndex = urlParts.findIndex(part => part === 'public');
        const signIndex = urlParts.findIndex(part => part === 'sign');
        
        if (publicIndex !== -1 && urlParts[publicIndex + 1] === bucket) {
          // Extract path after bucket in public URL
          filePath = urlParts.slice(publicIndex + 2).join('/');
        } else if (signIndex !== -1 && urlParts[signIndex + 1] === bucket) {
          // Extract path after bucket in signed URL
          filePath = urlParts.slice(signIndex + 2).join('/');
        } else {
          console.error(`Invalid file path format or bucket '${bucket}' not found in path:`, path);
          return null;
        }
      } else {
        // Extract the file path (everything after the bucket name)
        filePath = urlParts.slice(bucketIndex + 1).join('/');
      }
      
      console.log('Extracted file path:', filePath);
    } else {
      // Path is already a file path
      filePath = path;
    }

    // Get a signed URL that will work for 60 minutes (3600 seconds)
    console.log(`Creating signed URL for bucket: ${bucket}, path: ${filePath}`);
    const { data, error } = await supabase.storage
      .from(bucket)
      .createSignedUrl(filePath, 3600);

    if (error) {
      console.error('Error creating signed URL:', error);
      return null;
    }

    return data.signedUrl;
  } catch (error) {
    console.error('Error in getSignedUrl:', error);
    return null;
  }
};